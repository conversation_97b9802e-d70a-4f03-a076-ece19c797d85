@echo off
chcp 65001 >nul
echo === 快速推送简历 ===

REM Git路径
set GIT_PATH="C:\Program Files\Git\bin\git.exe"

REM 检查Git是否存在
if not exist %GIT_PATH% (
    echo 错误: 找不到Git
    pause
    exit /b 1
)

REM 快速推送（使用默认提交信息）
echo 添加文件...
%GIT_PATH% add .

echo 提交更改...
%GIT_PATH% commit -m "Quick update - %date% %time%"

echo 推送到GitHub...
%GIT_PATH% push origin main

if %errorlevel% equ 0 (
    echo.
    echo ✅ 推送成功!
    echo 仓库地址: https://github.com/ChuYuki/resume
) else (
    echo.
    echo ❌ 推送失败!
)

echo.
pause
