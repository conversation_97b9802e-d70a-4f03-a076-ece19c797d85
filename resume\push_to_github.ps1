# Resume Auto Push Script
# 自动推送简历到GitHub的脚本
# 使用方法: 在PowerShell中运行 .\push_to_github.ps1

Write-Host "=== Resume Auto Push Script ===" -ForegroundColor Green
Write-Host "正在推送简历到GitHub..." -ForegroundColor Yellow

# Git可执行文件路径
$gitPath = "C:\Program Files\Git\bin\git.exe"

# 检查Git是否存在
if (-not (Test-Path $gitPath)) {
    Write-Host "错误: 找不到Git，请确保Git已安装在默认路径" -ForegroundColor Red
    Write-Host "Git路径: $gitPath" -ForegroundColor Red
    pause
    exit 1
}

# 检查是否在正确的目录
if (-not (Test-Path "main.tex")) {
    Write-Host "错误: 当前目录不包含main.tex文件" -ForegroundColor Red
    Write-Host "请确保在简历项目根目录运行此脚本" -ForegroundColor Red
    pause
    exit 1
}

try {
    # 显示当前状态
    Write-Host "`n1. 检查文件状态..." -ForegroundColor Cyan
    & $gitPath status --short
    
    # 添加所有更改的文件
    Write-Host "`n2. 添加文件到暂存区..." -ForegroundColor Cyan
    & $gitPath add .
    
    # 检查是否有文件需要提交
    $status = & $gitPath status --porcelain
    if (-not $status) {
        Write-Host "没有文件需要提交，工作目录是干净的。" -ForegroundColor Yellow
        pause
        exit 0
    }
    
    # 获取提交信息
    $defaultMessage = "Update resume - $(Get-Date -Format 'yyyy-MM-dd HH:mm')"
    Write-Host "`n请输入提交信息 (直接回车使用默认信息): " -ForegroundColor Cyan -NoNewline
    $commitMessage = Read-Host
    
    if ([string]::IsNullOrWhiteSpace($commitMessage)) {
        $commitMessage = $defaultMessage
    }
    
    # 提交更改
    Write-Host "`n3. 提交更改..." -ForegroundColor Cyan
    Write-Host "提交信息: $commitMessage" -ForegroundColor Gray
    & $gitPath commit -m $commitMessage
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "提交失败!" -ForegroundColor Red
        pause
        exit 1
    }
    
    # 推送到GitHub
    Write-Host "`n4. 推送到GitHub..." -ForegroundColor Cyan
    & $gitPath push origin main
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✅ 成功推送到GitHub!" -ForegroundColor Green
        Write-Host "仓库地址: https://github.com/ChuYuki/resume" -ForegroundColor Blue
    } else {
        Write-Host "`n❌ 推送失败!" -ForegroundColor Red
        Write-Host "请检查网络连接和GitHub身份验证" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "`n❌ 脚本执行出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
pause
